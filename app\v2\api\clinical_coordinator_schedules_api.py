from fastapi import status, APIRouter, Depends, Response, Request, HTTPException
from v2.schema import (
    ResponseSchema,
    CoordinatorCaseListSchema,
    User,
    ResponseDictSchema,
    CoordLAAOProcedureSchema,
    CoordinatorQueryParams,
    CoordLAAOHistoryUpdate,
    ConsultVisitUpdateSchema,
    PreOpLabUpdateSchema,
    CoordLAAOProcedureUpdateSchema,
    ProcedureTypeSchema,
    ClinicalCoordCreatePatient,
    CreateBasePatient,
)
from v2.utils import (
    api_response,
    authorizer,
    get_cha2ds2_vasc_score,
    get_has_bled_risk_score,
    get_ambra_image_url,
    embedded_data_points_agent,
    get_patient_data,
    get_default_medication,
)
from pymongo.database import Database, DBRef
from datetime import datetime, timezone
from bson.objectid import ObjectId
from v2.constants import (
    COORDROLE,
    LAAO_PROCEDURE,
    ANESTHESIA_TYPE,
    LAAO_IMPLANT,
    CLINICIAN_ROLE,
)
from .create_patient_json import create_patient_using_json

router = APIRouter(tags=["V2 Clinical Co-ordinator Schedule"])


@router.get(
    "/cases/{case_id}/laao-procedure",
    response_model=ResponseDictSchema[CoordLAAOProcedureSchema],
)
async def get_specific_case_procedure_detail(
    request: Request,
    response: Response,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("case_summary")

        pipeline = [
            {"$match": {"_id": ObjectId(case_id), "deleted_at": None}},
            {
                "$lookup": {
                    "from": "implanting_physicians",
                    "localField": "implanting_physician_id.$id",
                    "foreignField": "_id",
                    "as": "physician_info",
                }
            },
            {
                "$unwind": {
                    "path": "$physician_info",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$match": {
                    "physician_info.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "laao_procedure_details",
                    "localField": "_id",
                    "foreignField": "case_id.$id",
                    "as": "case_detail_info",
                }
            },
            {
                "$unwind": {
                    "path": "$case_detail_info",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$match": {
                    "case_detail_info.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "device_type",
                    "localField": "case_detail_info.device_type.$id",
                    "foreignField": "_id",
                    "as": "device_info",
                }
            },
            {
                "$lookup": {
                    "from": "referring_provider_credentials",
                    "localField": "credential.$id",
                    "foreignField": "_id",
                    "as": "credential_info",
                }
            },
            {
                "$lookup": {
                    "from": "device_type",
                    "pipeline": [],
                    "as": "device_type_opt",
                }
            },
            {"$unwind": {"path": "$device_info", "preserveNullAndEmptyArrays": True}},
            {
                "$match": {
                    "device_info.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "localField": "case_detail_info.anesthesia_type.$id",
                    "foreignField": "_id",
                    "as": "anesthesia",
                }
            },
            {"$unwind": {"path": "$anesthesia", "preserveNullAndEmptyArrays": True}},
            {
                "$match": {
                    "anesthesia.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "pipeline": [
                        {
                            "$match": {
                                "deleted_at": None,
                            }
                        },
                        {
                            "$lookup": {
                                "from": "selection_type",
                                "localField": "selection_type_id.$id",
                                "foreignField": "_id",
                                "as": "anesthesia_option_selection",
                            }
                        },
                        {
                            "$match": {
                                "anesthesia_option_selection.name": ANESTHESIA_TYPE,
                                "anesthesia_option_selection.deleted_at": None,
                            }
                        },
                    ],
                    "as": "anesthesia_option",
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "localField": "case_detail_info.complication_id.$id",
                    "foreignField": "_id",
                    "as": "complication_info",
                }
            },
            {
                "$unwind": {
                    "path": "$complication_info",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$match": {
                    "complication_info.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "pipeline": [
                        {
                            "$match": {
                                "deleted_at": None,
                            }
                        },
                        {
                            "$lookup": {
                                "from": "selection_type",
                                "localField": "selection_type_id.$id",
                                "foreignField": "_id",
                                "as": "options",
                            }
                        },
                        {
                            "$match": {
                                "options.name": LAAO_IMPLANT,
                                "options.deleted_at": None,
                            }
                        },
                    ],
                    "as": "complication_option",
                }
            },
            {
                "$project": {
                    "case_id": "$_id",
                    "case_detail_id": "$case_detail_info._id",
                    "procedure_date": "$procedure_date",
                    "procedure_time": "$procedure_time",
                    "implanting_physician": {
                        "id": "$physician_info._id",
                        "name": {
                            "$concat": [
                                "$physician_info.first_name",
                                " ",
                                "$physician_info.last_name",
                                ", ",
                                {"$ifNull": ["$physician_info.credential", ""]},
                            ]
                        },
                    },
                    "device": {
                        "selected": {
                            "id": "$device_info._id",
                            "name": "$device_info.name",
                            "device_size": "$case_detail_info.device_size",
                        },
                        "options": {
                            "$map": {
                                "input": "$device_type_opt",
                                "as": "option",
                                "in": {
                                    "id": "$$option._id",
                                    "name": "$$option.name",
                                    "device_size": "$$option.device_size",
                                },
                            }
                        },
                    },
                    "anesthesia": {
                        "selected": {
                            "id": "$anesthesia._id",
                            "name": "$anesthesia.name",
                        },
                        "options": {
                            "$map": {
                                "input": "$anesthesia_option",
                                "as": "option",
                                "in": {"id": "$$option._id", "name": "$$option.name"},
                            }
                        },
                    },
                    "leak": "$case_detail_info.leak",
                    "leak_value": "$case_detail_info.leak_value",
                    "complication": {
                        "selected": {
                            "id": {
                                "$cond": {
                                    "if": {"$ne": ["$complication_info._id", None]},
                                    "then": "$complication_info._id",
                                    "else": None,
                                }
                            },
                            "name": {
                                "$cond": {
                                    "if": {"$ne": ["$complication_info.name", None]},
                                    "then": "$complication_info.name",
                                    "else": None,
                                }
                            },
                            "complication_other": "$case_detail_info.complication_other",
                            "complication_present": "$case_detail_info.complication_present",
                        },
                        "options": {
                            "$map": {
                                "input": "$complication_option",
                                "as": "option",
                                "in": {"id": "$$option._id", "name": "$$option.name"},
                            }
                        },
                    },
                    "afib_ablation": "$afib_ablation",
                }
            },
        ]
        data = collection.aggregate(pipeline).to_list()

        if data:
            return api_response(data[0], "Case fetched", "success")
        return api_response([], "No cases found", "success")

    except Exception:
        raise


@router.get(
    "/clinical/schedules", response_model=ResponseSchema[CoordinatorCaseListSchema]
)
async def get_clinical_coordinator_site_schedules_by_interval(
    request: Request,
    response: Response,
    queryparam: CoordinatorQueryParams = Depends(),
    token_data: dict = Depends(authorizer),
):
    try:
        user_data = User(**token_data.get("data", {}))
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("case_summary")

        # current_date = datetime.now(timezone.utc).date()
        # current_datetime = datetime.combine(
        #     current_date, datetime.min.time(), tzinfo=timezone.utc
        # )

        patient_firstname_condition = {}
        if queryparam.patient_name:
            patient_firstname_condition["patient.name"] = {
                "$regex": f"{queryparam.patient_name}",
                "$options": "i",
            }
        patient_conditions = {"patient_details.deleted_at": None}
        if queryparam.patient_dob:
            patient_conditions["patient_details.dob"] = queryparam.patient_dob
        if queryparam.patient_sex:
            patient_conditions["patient_details.sex"] = queryparam.patient_sex

        collection_conditions = {"deleted_at": None}
        procedure_detail_condition = {}
        postop_detail_condition = {}

        if queryparam.type == "procedure":
            procedure_detail_condition.update(
                {
                    "laao_procedure_details.deleted_at": None,
                    # "laao_procedure_details.device_deployed": False,
                }
            )
            # collection_conditions["procedure_date"] = queryparam.case_date
            collection_conditions = {
                "procedure_date": {
                    "$gte": queryparam.case_date,  # From date
                    "$lte": queryparam.to_date,  # To date
                }
            }
        elif queryparam.type == "pre-op":

            collection_conditions.update(
                {
                    "$expr": {
                        "$eq": ["$procedure_date", None],
                        # "$or": [
                        #     {"$eq":["$procedure_date", None]},
                        #     # {"$gt":[{"$toDate":"$procedure_date"}, current_datetime]},
                        # ]
                    }
                }
            )
        # elif queryparam.type == "post-op":
        #     procedure_detail_condition.update(
        #         {
        #             "laao_procedure_details.deleted_at": None,
        #             "laao_procedure_details.device_deployed": True,
        #         }
        #     )
        #     collection_conditions.update({"$expr": {"$ne": ["$procedure_date", None]}})
        #     postop_detail_condition.update(
        #         {
        #             "post_op_details.deleted_at": None,
        #             "$expr": {
        #                 "$or": [
        #                     {
        #                         "$and": [
        #                             {
        #                                 "$eq": [
        #                                     "$post_op_details.follow_ups_1_yr.date",
        #                                     queryparam.case_date,
        #                                 ]
        #                             },
        #                             {
        #                                 "$eq": [
        #                                     "$post_op_details.follow_ups_1_yr.completed",
        #                                     False,
        #                                 ]
        #                             },
        #                         ]
        #                     },
        #                     {
        #                         "$and": [
        #                             {
        #                                 "$eq": [
        #                                     "$post_op_details.follow_ups_45_days.date",
        #                                     queryparam.case_date,
        #                                 ]
        #                             },
        #                             {
        #                                 "$eq": [
        #                                     "$post_op_details.follow_ups_45_days.completed",
        #                                     False,
        #                                 ]
        #                             },
        #                         ]
        #                     },
        #                     {
        #                         "$and": [
        #                             {
        #                                 "$eq": [
        #                                     "$post_op_details.follow_ups_6_months.date",
        #                                     queryparam.case_date,
        #                                 ]
        #                             },
        #                             {
        #                                 "$eq": [
        #                                     "$post_op_details.follow_ups_6_months.completed",
        #                                     False,
        #                                 ]
        #                             },
        #                         ]
        #                     },
        #                     {"$eq": ["$procedure_date", queryparam.case_date]},
        #                 ]
        #             },
        #         }
        #     )
        elif queryparam.type == "completed":
            procedure_detail_condition.update(
                {
                    "laao_procedure_details.deleted_at": None,
                    "laao_procedure_details.device_deployed": True,
                }
            )
            collection_conditions.update(
                {
                    "$expr": {
                        "$ne": ["$procedure_date", None],
                    }
                }
            )
            # postop_detail_condition.update(
            #     {
            #         "post_op_details.follow_ups_1_yr.completed": True,
            #         "$expr": {
            #             "$and": [
            #                 {
            #                     "$gte": [
            #                         "$post_op_details.follow_ups_1_yr.date",
            #                         queryparam.completed_start_date,
            #                     ]
            #                 },
            #                 {
            #                     "$lte": [
            #                         "$post_op_details.follow_ups_1_yr.date",
            #                         queryparam.completed_end_date,
            #                     ]
            #                 },
            #             ]
            #         },
            #         "post_op_details.deleted_at": None,
            #     }
            # )

            # For completed type, we want cases where:
            # 1. Procedure has been completed (device_deployed = true)
            # 2. Either has completed follow-ups in the date range OR procedure was done in the date range
            # Simplified condition: just match cases with procedure dates in the range
            # This will include all cases that had procedures completed in the date range
            postop_detail_condition.update(
                {
                    "$expr": {
                        "$and": [
                            {"$ne": ["$procedure_date", None]},
                            {
                                "$gte": [
                                    "$procedure_date",
                                    queryparam.completed_start_date,
                                ]
                            },
                            {
                                "$lte": [
                                    "$procedure_date",
                                    queryparam.completed_end_date,
                                ]
                            },
                        ]
                    }
                }
            )

        else:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, "invalid option type")

        data = collection.aggregate(
            [
                {"$match": collection_conditions},
                {
                    "$lookup": {
                        "from": "laao_procedure_details",
                        "localField": "_id",
                        "foreignField": "case_id.$id",
                        "as": "laao_procedure_details",
                    }
                },
                {
                    "$unwind": {
                        "path": "$laao_procedure_details",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {"$match": procedure_detail_condition},
                {
                    "$lookup": {
                        "from": "post_op_details",
                        "localField": "_id",
                        "foreignField": "case_id.$id",
                        "as": "post_op_details",
                    }
                },
                {
                    "$unwind": {
                        "path": "$post_op_details",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {"$match": postop_detail_condition},
                {
                    "$lookup": {
                        "from": "user_site_mapping",
                        "localField": "site_id.$id",
                        "foreignField": "site_id.$id",
                        "as": "user_site_map_details",
                    }
                },
                {
                    "$unwind": {
                        "path": "$user_site_map_details",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$lookup": {
                        "from": "user",
                        "localField": "user_site_map_details.user_id.$id",
                        "foreignField": "_id",
                        "as": "user_details",
                    }
                },
                {
                    "$unwind": {
                        "path": "$user_details",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$lookup": {
                        "from": "medicines",
                        "localField": "anticoagulant.medicine.$id",
                        "foreignField": "_id",
                        "as": "anticoagulant_info",
                    }
                },
                # {
                #     "$match": {
                #         "anticoagulant_info.deleted_at": None,
                #     }
                # },
                {
                    "$match": {
                        "user_details.keycloak_id": user_data.sub,
                        "user_details.deleted_at": None,
                        # "user_details.role": COORDROLE,
                        "$expr": {
                            "$or": [
                                {"$eq": ["$user_details.role", COORDROLE]},
                                {"$eq": ["$user_details.role", CLINICIAN_ROLE]},
                            ]
                        },
                    }
                },
                {
                    "$lookup": {
                        "from": "patient",
                        "localField": "patient_id.$id",
                        "foreignField": "_id",
                        "as": "patient_details",
                    }
                },
                {
                    "$unwind": {
                        "path": "$patient_details",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$match": {
                        "patient_details.deleted_at": None,
                    }
                },
                {
                    "$lookup": {
                        "from": "referring_provider",
                        "localField": "patient_details.referring_providers.$id",
                        "foreignField": "_id",
                        "as": "referring_provider_details",
                    }
                },
                {
                    "$unwind": {
                        "path": "$referring_provider_details",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {"$match": {"referring_provider_details.deleted_at": None}},
                {
                    "$lookup": {
                        "from": "laao_string_selections",
                        "localField": "rationale.$id",
                        "foreignField": "_id",
                        "as": "rationale_info",
                    }
                },
                {
                    "$unwind": {
                        "path": "$rationale_info",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$match": {
                        "rationale_info.deleted_at": None,
                    }
                },
                {
                    "$lookup": {
                        "from": "post_op_details",
                        "let": {"case": "$_id"},
                        "pipeline": [
                            {"$match": {"$expr": {"$eq": ["$case_id.$id", "$$case"]}}},
                            {
                                "$lookup": {
                                    "from": "laao_string_selections",
                                    "localField": "discharge_plan.$id",
                                    "foreignField": "_id",
                                    "as": "discharge_info",
                                }
                            },
                            {
                                "$unwind": {
                                    "path": "$discharge_info",
                                    "preserveNullAndEmptyArrays": True,
                                }
                            },
                            {"$project": {"discharge_plan": "$discharge_info.name"}},
                        ],
                        "as": "post_op_info",
                    }
                },
                {
                    "$unwind": {
                        "path": "$post_op_info",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$addFields": {
                        "patient": {
                            "id": "$patient_details._id",
                            "name": {
                                "$concat": [
                                    {
                                        "$ifNull": [
                                            "$patient_details.first_name",
                                            "",
                                        ]
                                    },
                                    " ",
                                    {"$ifNull": ["$patient_details.last_name", ""]},
                                ]
                            },
                            "age": {
                                "$subtract": [
                                    {
                                        "$year": datetime.now(timezone.utc)
                                    },  # Current year
                                    {
                                        "$year": {
                                            "$dateFromString": {
                                                "dateString": "$patient_details.dob"
                                            }
                                        }
                                    },  # Year of DOB
                                ]
                            },
                            "sex": "$patient_details.sex",
                            "anticipated": "$post_op_info.discharge_plan",
                            "address": {
                                "$cond": [
                                    {
                                        "$and": [
                                            {"$ne": ["$patient_details.city", None]},
                                            {"$ne": ["$patient_details.city", ""]},
                                        ]
                                    },
                                    {
                                        "$concat": [
                                            "$patient_details.city",
                                            {
                                                "$cond": [
                                                    {
                                                        "$and": [
                                                            {
                                                                "$ne": [
                                                                    "$patient_details.state",
                                                                    None,
                                                                ]
                                                            },
                                                            {
                                                                "$ne": [
                                                                    "$patient_details.state",
                                                                    "",
                                                                ]
                                                            },
                                                        ]
                                                    },
                                                    {
                                                        "$concat": [
                                                            ", ",
                                                            "$patient_details.state",
                                                        ]
                                                    },
                                                    "",
                                                ]
                                            },
                                        ]
                                    },
                                    {"$ifNull": ["$patient_details.state", ""]},
                                ]
                            },
                            "cta": "$cta",
                            "tee": "$tee",
                            "afib_ablation": "$afib_ablation",
                            "rationale": {"$ifNull": ["$rationale_info.name", ""]},
                            "referring_provider": {
                                "id": "$referring_provider_details._id",
                                "name": {
                                    "$concat": [
                                        {
                                            "$ifNull": [
                                                "$referring_provider_details.first_name",
                                                None,
                                            ]
                                        },
                                        " ",
                                        {
                                            "$ifNull": [
                                                "$referring_provider_details.last_name",
                                                None,
                                            ]
                                        },
                                        ", ",
                                        {
                                            "$ifNull": [
                                                "$credential_info.name",
                                                "",
                                            ]
                                        },
                                    ]
                                },
                            },
                        }
                    }
                },
                {"$match": patient_firstname_condition},
                {"$sort": {"procedure_date": 1, "procedure_time": 1}},
                {
                    "$project": {
                        "_id": 0,
                        "case_id": "$_id",
                        "patient": 1,
                        "procedure_date": "$procedure_date",
                        "procedure_time": "$procedure_time",
                        "anticoagulant": {
                            "$map": {
                                "input": "$anticoagulant_info",
                                "as": "option",
                                "in": {"id": "$$option._id", "name": "$$option.name"},
                            }
                        },
                    }
                },
            ]
        ).to_list()

        # Debug: Print query results and conditions
        print(f"DEBUG: Query type: {queryparam.type}")
        if queryparam.type == "completed":
            print(f"DEBUG: Completed date range: {queryparam.completed_start_date} to {queryparam.completed_end_date}")
            print(f"DEBUG: postop_detail_condition: {postop_detail_condition}")
        print(f"DEBUG: Found {len(data)} cases")
        print(f"DEBUG: Sample data: {data[:1] if data else 'No data'}")

        response.status_code = status.HTTP_200_OK
        for case in data:
            patient_id = case.get("patient", {}).get("id")

            anticoagulation = case.get("anticoagulant")
            case_id = case.get("case_id")

            ambra_params = {
                "filter.patientid.equals": str(patient_id),
                "filter.modality.equals": "CT",
            }

            # Check verified status for this case
            patient_registry_collection = mongo_client.get_collection("patient_registry_details")
            registry_data = patient_registry_collection.find_one(
                {"case_id.$id": ObjectId(case_id), "deleted_at": None}
            )



            verified_status = False

            if registry_data:
                # Get all section names from the template (excluding case_id, created_at, updated_at, deleted_at)
                excluded_keys = {"case_id", "created_at", "updated_at", "deleted_at", "_id"}
                section_keys = [key for key in registry_data.keys() if key not in excluded_keys]

                # Check if all sections have verified.value = "True" and verified.modified_by = "ABSTRACTOR"
                all_verified = True

                for section_key in section_keys:
                    section_data = registry_data.get(section_key, {})
                    verified_field = section_data.get("verified", {})

                    # Check if verified field has the expected structure and values
                    if (isinstance(verified_field, dict) and
                        verified_field.get("value") == "True"):
                        #   and verified_field.get("modified_by") == "ABSTRACTOR"):
                        
                        continue  # This section is verified
                    else:
                        all_verified = False
                        break  # No need to check further sections

                verified_status = all_verified

            # Update patient data
            patient_data = case.get("patient", {})
            patient_data.update(
                {
                    "cha2ds2_vasc": get_cha2ds2_vasc_score(case_id, mongo_client),
                    "has_bled_score": get_has_bled_risk_score(case_id, mongo_client),
                    "anticoagulation": anticoagulation,
                    "study": get_ambra_image_url(ambra_params, mongo_client),
                }
            )

            # Add verified status at the case level (outer level)
            case["verified"] = verified_status

            # Get NCDR registry status for this case
            ncdr_registry_collection = mongo_client.get_collection("ncdr_registry_status")

            # Try different query approaches
            ncdr_data = ncdr_registry_collection.find_one(
                {"case_id.$id": ObjectId(case_id)}
            )

            # If not found, try without ObjectId conversion
            if not ncdr_data:
                ncdr_data = ncdr_registry_collection.find_one(
                    {"case_id.$id": case_id}
                )

            # Debug NCDR data
            patient_name = case.get("patient", {}).get("name", "")
            print(f"DEBUG: Checking NCDR data for case {case_id} ({patient_name})")
            print(f"DEBUG: NCDR data found: {ncdr_data is not None}")
            if ncdr_data:
                submissions = ncdr_data.get("submissions", [])
                print(f"DEBUG: Number of submissions: {len(submissions)}")
                if submissions:
                    print(f"DEBUG: Latest submission: {submissions[-1].get('submission_id')} - {submissions[-1].get('status')}")
            else:
                # Try to find any NCDR data to see what the structure looks like
                sample_ncdr = ncdr_registry_collection.find_one()
                if sample_ncdr:
                    print(f"DEBUG: Sample NCDR record structure: {sample_ncdr.get('case_id')}")
                    print(f"DEBUG: Sample case_id type: {type(sample_ncdr.get('case_id', {}).get('$id'))}")
                else:
                    print("DEBUG: No NCDR records found in collection")

            ncdr_status = {
                "last_submission": None,
                "submission_history": []
            }

            if ncdr_data and ncdr_data.get("submissions"):
                submissions = ncdr_data.get("submissions", [])

                # Sort submissions by timestamp (most recent first)
                sorted_submissions = sorted(
                    submissions,
                    key=lambda x: x.get("timestamp", {}).get("$date", ""),
                    reverse=True
                )

                # Get the last (most recent) submission
                if sorted_submissions:
                    last_submission = sorted_submissions[0]
                    ncdr_status["last_submission"] = {
                        "submission_id": last_submission.get("submission_id"),
                        "status": last_submission.get("status"),
                        "result": last_submission.get("result"),
                        "timestamp": last_submission.get("timestamp", {}).get("$date")
                    }

                # Get all submission history
                ncdr_status["submission_history"] = [
                    {
                        "submission_id": submission.get("submission_id"),
                        "status": submission.get("status"),
                        "result": submission.get("result"),
                        "timestamp": submission.get("timestamp", {}).get("$date")
                    }
                    for submission in sorted_submissions
                ]

            # Add NCDR status at the case level
            case["ncdr_registry_status"] = ncdr_status

        if data:
            return api_response(data, "Case list fetched", "success")
        else:
            return api_response([], "No records found", "success")
    except Exception:
        raise


@router.put("/cases/{case_id}/history")
async def update_clinical_preop_detail(
    request: Request,
    response: Response,
    body: CoordLAAOHistoryUpdate,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        filtered_data = body.model_dump(exclude_unset=True)
        # filtered_data = {k: v for k, v in update_values.items() if v}
        mongo_client: Database = request.app.database
        if filtered_data:
            social_history = filtered_data.pop("social_history", None)
            relavant_medical_history = filtered_data.pop(
                "relavant_medical_history", None
            )
            patient_collection = mongo_client.get_collection("patient")
            case_collection = mongo_client.get_collection("case_summary")
            laao_procedure_details_collection = mongo_client.get_collection(
                "laao_procedure_details"
            )
            case_res = case_collection.find_one_and_update(
                {"_id": ObjectId(case_id)},
                {"$set": filtered_data},
                return_document=True,
                projection={"patient_id": "$patient_id.$id"},
            )
            if case_res:
                patient_update_res = type(
                    "MockUpdateResult", (), {"matched_count": 0}
                )()
                patient_update_rel = type(
                    "MockUpdateResult", (), {"matched_count": 0}
                )()

                if social_history:
                    patient_id = case_res.get("patient_id")
                    home_address = social_history.pop("home_address", {})
                    social_history.update(home_address if home_address else {})
                    patient_update_res = patient_collection.update_one(
                        {"_id": patient_id}, {"$set": social_history}
                    )

                if relavant_medical_history:
                    patient_update_rel = laao_procedure_details_collection.update_one(
                        {"case_id.$id": ObjectId(case_id)},
                        {"$set": relavant_medical_history},
                    )

                if (
                    patient_update_res.matched_count > 0
                    or patient_update_rel.matched_count > 0
                ):
                    patient_data = get_patient_data(
                        case_id, mongo_client, request.method
                    )
                    # patient_id = str(
                    #     patient_data["patient_id"].id
                    #     if hasattr(patient_data["patient_id"], "id")
                    #     else patient_data["patient_id"]
                    # )
                    embedded_data_points_agent(case_id, token_data["token"])
                    return api_response([], "Case record updated", "success")
                else:
                    if case_res:
                        return api_response([], "Case record updated", "success")
                    else:
                        raise HTTPException(
                            status.HTTP_404_NOT_FOUND, "patient data not found"
                        )
            else:
                raise HTTPException(status.HTTP_404_NOT_FOUND, "case data not found")
        else:
            return api_response([], "Nothing to update", "success")
    except Exception:
        raise


@router.post("/{case_id}/consult-visit", response_model=ResponseSchema)
async def add_consult_visit_for_a_patient(
    request: Request,
    response: Response,
    body: ConsultVisitUpdateSchema,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        filtered_data = body.model_dump(exclude_unset=True)
        # filtered_data = {k: v for k, v in update_values.items() if v is not None}

        mongo_client: Database = request.app.database
        case_collection = mongo_client.get_collection("case_summary")

        case_data = case_collection.find_one({"_id": ObjectId(case_id)})
        visit_type = None

        if case_data:
            proce_date = case_data.get("procedure_date")
            if proce_date:
                visit_date = datetime.strptime(body.visit_date, "%Y-%m-%d")
                proce_date = datetime.strptime(proce_date, "%Y-%m-%d")
                if proce_date >= visit_date:
                    visit_type = "pre-op"
                elif proce_date < visit_date:
                    visit_type = "post-op"
            else:
                visit_type = "pre-op"
        else:
            visit_type = "pre-op"

        if not (filtered_data):
            return api_response([], "Nothing to update", "success")

        if case_id:
            filtered_data.update(
                {
                    "case_id": DBRef("case_summary", ObjectId(case_id)),
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "visit_type": visit_type,
                    "deleted_at": None,
                }
            )
        else:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, "provide valid patient ID")

        collection = mongo_client.get_collection("consult_visit")
        res = collection.insert_one(filtered_data)

        if res.inserted_id:
            embedded_data_points_agent(case_id, token_data["token"])
            response.status_code = status.HTTP_200_OK
            return api_response([], "record inserted successfully", "success")

        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return api_response([], "record insertion failed", "success")

    except Exception:
        raise


@router.put("/{case_id}/pre-op-lab", response_model=ResponseSchema)
async def add_pre_op_lab_for_a_patient(
    request: Request,
    response: Response,
    body: PreOpLabUpdateSchema,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        update_values = body.model_dump()
        filtered_data = {k: v for k, v in update_values.items() if v is not None}

        if not (filtered_data):
            return api_response([], "Nothing to update", "success")

        filtered_data.update(
            {
                "case_id": DBRef("case_summary", ObjectId(case_id)),
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "deleted_at": None,
                "rhythm_id": body.rhythm_id,
                "hemoglobin_updated_at": datetime.now().isoformat(),
                "platelets_updated_at": datetime.now().isoformat(),
                "creatinine_updated_at": datetime.now().isoformat(),
                "egfr_updated_at": datetime.now().isoformat(),
            }
        )

        mongo_client: Database = request.app.database

        case_collection = mongo_client.get_collection("case_summary")
        case_data = case_collection.find_one({"_id": ObjectId(case_id)})

        collection = mongo_client.get_collection("lab_details")
        res = collection.insert_one(filtered_data)

        if res.inserted_id:
            embedded_data_points_agent(case_id, token_data["token"])
            response.status_code = status.HTTP_200_OK
            return api_response([], "record inserted successfully", "success")

        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return api_response([], "record insertion failed", "success")

    except Exception:
        raise


@router.put("/cases/{case_id}/laao-procedure", response_model=ResponseSchema)
async def update_clinical_specific_procedure_detail(
    request: Request,
    response: Response,
    case_id: str,
    body: CoordLAAOProcedureUpdateSchema,
    token_data: dict = Depends(authorizer),
):
    try:
        filtered_data = body.model_dump(exclude_unset=True)
        # filtered_data = {k: v for k, v in body_data.items() if v}
        mongo_client: Database = request.app.database

        if filtered_data:
            procedure_detail: dict = filtered_data.pop("procedure_detail", {})
            case_summary_collection = mongo_client.get_collection("case_summary")
            case_update_res = case_summary_collection.update_one(
                {"_id": ObjectId(case_id)}, {"$set": filtered_data}
            )
            if procedure_detail and case_update_res.matched_count > 0:
                detail_collection = mongo_client.get_collection(
                    "laao_procedure_details"
                )
                # procedure_detail = {k: v for k, v in procedure_detail.items() if v}
                device_collection = mongo_client.get_collection("device_type")
                device_type = procedure_detail.get("device_type")
                device_size = procedure_detail.get("device_size")

                if device_type:
                    device_type = device_type.id

                if device_type != None and device_size != None:
                    filtered_data.update(
                        {"device_type": DBRef("device_type", ObjectId(device_type))}
                    )
                    data = device_collection.find_one({"_id": ObjectId(device_type)})
                    if data:
                        assert device_size in data.get(
                            "device_size", []
                        ), "Device Size is not available in the selected Device"
                    else:
                        raise ValueError("device_type not found")
                elif device_type != None:
                    filtered_data.update(
                        {"device_type": DBRef("device_type", ObjectId(device_type))}
                    )
                    device_data = detail_collection.aggregate(
                        [
                            {"$match": {"case_id.$id": ObjectId(case_id)}},
                            {
                                "$lookup": {
                                    "from": "device_type",
                                    "pipeline": [
                                        {"$match": {"_id": ObjectId(device_type)}}
                                    ],
                                    "as": "new_device",
                                }
                            },
                            {"$unwind": "$new_device"},
                        ]
                    ).to_list()
                    if device_data:
                        data = device_data[0]
                        assert data.get("device_size") in data.get(
                            "new_device", {}
                        ).get(
                            "device_size", []
                        ), "Existing device size is not available in this device model"
                elif device_size != None:
                    data = detail_collection.aggregate(
                        [
                            {"$match": {"case_id.$id": ObjectId(case_id)}},
                            {
                                "$lookup": {
                                    "from": "device_type",
                                    "localField": "device_type.$id",
                                    "foreignField": "_id",
                                    "as": "device",
                                }
                            },
                            {"$unwind": "$device"},
                        ]
                    ).to_list()
                    if data:
                        assert device_size in data[0].get("device", {}).get(
                            "device_size", []
                        ), "Device Size is not available in the selected Device"
                    else:
                        raise ValueError("device_type not found")

                update_res = detail_collection.update_one(
                    {"case_id.$id": ObjectId(case_id)}, {"$set": procedure_detail}
                )

                if update_res.matched_count > 0:
                    # patient_data = get_patient_data(
                    #     case_id, mongo_client, request.method
                    # )
                    # patient_id = str(
                    #     patient_data["patient_id"].id
                    #     if hasattr(patient_data["patient_id"], "id")
                    #     else patient_data["patient_id"]
                    # )
                    embedded_data_points_agent(case_id, token_data["token"])
                    response.status_code = status.HTTP_200_OK
                    return api_response(
                        [],
                        "clinical coordinator laao procedure details updated successfully",
                        "success",
                    )
                else:
                    raise HTTPException(status.HTTP_404_NOT_FOUND, "Case not found")
            else:
                raise HTTPException(status.HTTP_404_NOT_FOUND, "case not found")
        else:
            response.status_code = status.HTTP_200_OK
            return api_response([], "Nothing to update", "success")
    except Exception:
        raise


@router.get(
    "/clinical/implanting-physician",
    response_model=ResponseSchema[ProcedureTypeSchema],
)
async def get_clinical_coordinator_implanting_physician(
    request: Request, response: Response, token_data: dict = Depends(authorizer)
):
    try:
        user_data = User(**token_data.get("data", {}))
        keycloak_id = user_data.sub

        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("user")

        if COORDROLE in user_data.realm_access.get(
            "roles", []
        ) or CLINICIAN_ROLE in user_data.realm_access.get("roles", []):
            data = list(
                collection.aggregate(
                    [
                        {"$match": {"deleted_at": None, "keycloak_id": keycloak_id}},
                        {
                            "$lookup": {
                                "from": "user_site_mapping",
                                "localField": "_id",
                                "foreignField": "user_id.$id",
                                "as": "user_site_mapping_details",
                            }
                        },
                        {"$unwind": "$user_site_mapping_details"},
                        {"$match": {"user_site_mapping_details.deleted_at": None}},
                        {
                            "$lookup": {
                                "from": "implanting_physicians",
                                "let": {
                                    "site_id": "$user_site_mapping_details.site_id.$id"
                                },
                                "pipeline": [
                                    {"$unwind": "$sites"},
                                    {
                                        "$match": {
                                            "$expr": {
                                                "$eq": ["$sites.$id", "$$site_id"]
                                            }
                                        }
                                    },
                                ],
                                "as": "implanting_physicians_details",
                            }
                        },
                        {
                            "$unwind": {
                                "path": "$implanting_physicians_details",
                                "preserveNullAndEmptyArrays": True,
                            }
                        },
                        {
                            "$project": {
                                "_id": 0,
                                "id": "$implanting_physicians_details._id",
                                "name": {
                                    "$concat": [
                                        "$implanting_physicians_details.first_name",
                                        " ",
                                        "$implanting_physicians_details.last_name",
                                        ", ",
                                        {
                                            "$ifNull": [
                                                "$implanting_physicians_details.credential",
                                                "",
                                            ]
                                        },
                                    ]
                                },
                            }
                        },
                    ]
                )
            )

            response.status_code = status.HTTP_200_OK
            if data:
                return api_response(data, "Procedure record fetched", "success")
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="No record found"
                )
        else:
            raise HTTPException(
                status.HTTP_403_FORBIDDEN, "you don't have access to this route"
            )
    except Exception:
        raise


@router.post("/clinical/patients")
async def clinical_coordinator_create_patient_record(
    request: Request,
    response: Response,
    body: ClinicalCoordCreatePatient,
    token_data: dict = Depends(authorizer),
):
    try:
        parsed_data = body.model_dump()
        mongo_client: Database = request.app.database

        # Get the User token
        user_data = User(**token_data.get("data", {}))
        keycloak_id = user_data.sub
        user_collection = mongo_client.get_collection("user")
        if COORDROLE in user_data.realm_access.get(
            "roles", []
        ) or CLINICIAN_ROLE in user_data.realm_access.get("roles", []):
            # Get the Site id
            data = list(
                user_collection.aggregate(
                    [
                        {"$match": {"deleted_at": None, "keycloak_id": keycloak_id}},
                        {
                            "$lookup": {
                                "from": "user_site_mapping",
                                "localField": "_id",
                                "foreignField": "user_id.$id",
                                "as": "user_site_mapping_details",
                            }
                        },
                        {"$unwind": "$user_site_mapping_details"},
                        {"$match": {"user_site_mapping_details.deleted_at": None}},
                        {
                            "$project": {
                                "_id": 1,
                                "email_id": 1,
                                "site_id": "$user_site_mapping_details.site_id",
                            }
                        },
                    ]
                )
            )
            site_id = data[0]["site_id"].id

            # Get LAAO Procedure type id
            pt_collection = mongo_client.get_collection("procedure_type")
            pt_data = pt_collection.find_one({"name": LAAO_PROCEDURE})
            if pt_data:
                procedure_type_id = str(pt_data.get("_id"))

            patient_collection = mongo_client.get_collection("patient")
            rationale = parsed_data.pop("rationale", None)
            rationale_other = parsed_data.pop("rationale_other", None)
            procedure_date = parsed_data.pop("procedure_date", None)
            procedure_time = parsed_data.pop("procedure_time", None)
            implanting_physician_id = parsed_data.pop("implanting_physician_id", None)

            # if parsed_data["referring_providers"]:
            #     referring_provider_id = parsed_data.pop("referring_providers", None)
            #     parsed_data["referring_providers"] = [
            #         DBRef("referring_providers", ObjectId(referring_provider_id))
            #     ]

            add_patient = patient_collection.insert_one(parsed_data)
            patient_id = add_patient.inserted_id
            # patient_id = ObjectId()

            # default_anticoagulation = get_default_medication(mongo_client, True)

            case_summary_collection = mongo_client.get_collection("case_summary")
            case_detail = {
                "procedure_type_id": DBRef(
                    "procedure_type", ObjectId(procedure_type_id)
                ),
                "patient_id": DBRef("patient", ObjectId(patient_id)),
                "site_id": DBRef("site", ObjectId(site_id)),
                "implanting_physician_id": implanting_physician_id,
                "rep_id": None,
                "procedure_date": procedure_date,
                "procedure_time": procedure_time,
                "rationale": rationale,
                "rationale_other": rationale_other,
                "cta": False,
                "tee": False,
                "cta_image": False,
                "afib_ablation": False,
                "prior_ablation": None,
                "prior_ablation_other": None,
                # "anticoagulant": None,
                "truplan_pdf_filename": None,
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "deleted_at": None,
                "secondary_rationale": None,
                "secondary_rationale_other": None,
                "complication": None,
                "afib_classification_type_id": None,
                "truplan_upload_status": "Pending",
            }
            create_case = case_summary_collection.insert_one(case_detail)
            case_id = create_case.inserted_id
            # case_id = ObjectId()

            # if procedure_date:
            #     task_data = {
            #         "assigner_id": data[0]["_id"],
            #         "assignee_id": data[0]["_id"],
            #         "case_id": case_id,
            #         "email_id": str(data[0]["email_id"]),
            #         "procedure_date": procedure_date
            #     }
            #     create_follow_up_tasks(
            #         mongo_client=mongo_client,
            #         task_data=task_data,
            #         task_type=("task_sub_type", FOLLOW_UP_45_DAYS),
            #     )
            #     create_follow_up_tasks(
            #         mongo_client=mongo_client,
            #         task_data=task_data,
            #         task_type=("task_sub_type", FOLLOW_UP_6_MONTHS)
            #     )
            # return_response = {"patient_id": str(patient_id), "case_id": str(case_id)}

            create_patient_body = CreateBasePatient(
                **parsed_data, case_id=str(case_id), patient_id=str(patient_id)
            )

            # Call the second endpoint directly
            second_api_response = await create_patient_using_json(
                request=request,
                response=response,
                body=create_patient_body,
                token_data=token_data,
            )
            embedded_data_points_agent(str(case_id), token_data["token"])

            # Check verified status for all sections in patient_registry_details
            patient_registry_collection = mongo_client.get_collection("patient_registry_details")
            registry_data = patient_registry_collection.find_one(
                {"case_id.$id": ObjectId(case_id), "deleted_at": None}
            )

            verified_status = False
            if registry_data:
                # Get all section names from the template (excluding case_id, created_at, updated_at, deleted_at)
                excluded_keys = {"case_id", "created_at", "updated_at", "deleted_at", "_id"}
                section_keys = [key for key in registry_data.keys() if key not in excluded_keys]

                # Check if all sections have verified.value = "True" and verified.modified_by = "ABSTRACTOR"
                all_verified = True
                for section_key in section_keys:
                    section_data = registry_data.get(section_key, {})
                    verified_field = section_data.get("verified", {})

                    # Check if verified field has the expected structure and values
                    if (not isinstance(verified_field, dict) or
                        verified_field.get("value") != "True" or
                        verified_field.get("modified_by") != "ABSTRACTOR"):
                        all_verified = False
                        break

                verified_status = all_verified

            # Get NCDR registry status for this case
            ncdr_registry_collection = mongo_client.get_collection("ncdr_registry_status")
            ncdr_data = ncdr_registry_collection.find_one(
                {"case_id.$id": ObjectId(case_id)}
            )

            ncdr_status = {
                "last_submission": None,
                "submission_history": []
            }

            if ncdr_data and ncdr_data.get("submissions"):
                submissions = ncdr_data.get("submissions", [])

                # Sort submissions by timestamp (most recent first)
                sorted_submissions = sorted(
                    submissions,
                    key=lambda x: x.get("timestamp", {}).get("$date", ""),
                    reverse=True
                )

                # Get the last (most recent) submission
                if sorted_submissions:
                    last_submission = sorted_submissions[0]
                    ncdr_status["last_submission"] = {
                        "submission_id": last_submission.get("submission_id"),
                        "status": last_submission.get("status"),
                        "result": last_submission.get("result"),
                        "timestamp": last_submission.get("timestamp", {}).get("$date")
                    }

                # Get all submission history
                ncdr_status["submission_history"] = [
                    {
                        "submission_id": submission.get("submission_id"),
                        "status": submission.get("status"),
                        "result": submission.get("result"),
                        "timestamp": submission.get("timestamp", {}).get("$date")
                    }
                    for submission in sorted_submissions
                ]

            return_response = second_api_response["result"][0]
            return_response["verified"] = verified_status
            return_response["ncdr_registry_status"] = ncdr_status

            response.status_code = status.HTTP_201_CREATED
            return api_response(
                [return_response], "New patient case created", "success"
            )

        else:
            raise HTTPException(
                status.HTTP_403_FORBIDDEN, "you don't have access to this route"
            )
    except Exception:
        raise
