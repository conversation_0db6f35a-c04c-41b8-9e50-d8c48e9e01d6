from fastapi import APIRouter, Request, Response, Depends, status, HTTPException
from pymongo.database import Database, DBRef
from bson import ObjectId
import xml.etree.ElementTree as ET
import base64
import shutil
import zeep, pyzipper
import os, io
from datetime import datetime

from config import settings
from v2.schema import ResponseSchema
from v2.utils import (
    authorizer, api_response,
    get_field_ids_and_values,
    compute_hash, zip_and_encrypt_file,
    unzip_and_decrypt_file, validate_xml, construct_ncdr_xml
)

router = APIRouter(tags=["V2 Submission"], prefix="/ncdr/cases")

wsdl = settings.NCDR_WSDL
client = zeep.Client(wsdl=wsdl)

client_id = settings.NCDR_CLIENT_ID
client_id_bytes = client_id.encode("ascii")

registry = settings.NCDR_REGISTRY
secrets = settings.NCDR_SECRETS
enc_algo = settings.NCDR_ENC_ALGORITHM


@router.post("/{case_id}/submit", response_model=ResponseSchema)
async def submit_case(
    request: Request,
    response: Response,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        mongo_client: Database = request.app.database
        case_data = mongo_client.patient_registry_details.find_one(
            {"case_id": DBRef("case_summary", ObjectId(case_id))},
            {"_id": 0, "created_at": 0, "deleted_at": 0, "updated_at": 0}
        )
        if not case_data:
            raise HTTPException(status_code=404, detail="Case not found")

        case_data.pop("case_id", None)
        filtered_case_data = case_data

        updated_xml_path = construct_ncdr_xml(filtered_case_data, case_id)
        if not updated_xml_path:
            raise HTTPException(status_code=500, detail="Failed to generate XML")

        validation_errors = validate_xml(updated_xml_path)
        if validation_errors:
            raise HTTPException(status_code=400, detail=validation_errors)

        zip_file_path = zip_and_encrypt_file(updated_xml_path, case_id, secrets)
        zip_file_path = zip_file_path.replace("/", "//")

        with open(zip_file_path, "rb") as file:
            file_data = file.read()
        encoded_file_data = base64.b64encode(file_data).decode("ascii")

        submission_response = client.service.DQRSubmission(
            client_id,
            registry,
            compute_hash(secrets, enc_algo, client_id_bytes),
            os.path.basename(zip_file_path),
            encoded_file_data
        )

        submission_record = {
            "submission_id": submission_response.SubmissionID,
            "status": submission_response.Status,
            "result": None,
            "timestamp": datetime.utcnow()
        }

        mongo_client.ncdr_registry_status.update_one(
            {"case_id": DBRef("case_summary", case_id)},
            {
                "$setOnInsert": {"case_id": DBRef("case_summary", case_id)},
                "$push": {"submissions": submission_record}
            },
            upsert=True
        )

        response.status_code = status.HTTP_200_OK
        return api_response(
            {"status": submission_response.Status, "submission_id": submission_response.SubmissionID},
            "Submission successful",
            "success"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{case_id}/status", response_model=ResponseSchema)
async def get_ncdr_case_status(
    request: Request,
    response: Response,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        mongo_client: Database = request.app.database
        status_data = mongo_client.ncdr_registry_status.find_one({"case_id.$id": case_id})
        if not status_data or not status_data.get("submissions"):
            raise HTTPException(status_code=404, detail="No submissions found for this case")

        latest_submission = status_data["submissions"][-1]
        submission_id = latest_submission["submission_id"]

        status_response = client.service.DQRSubmissionResultBySubmissionGuid(
            client_id,
            registry,
            compute_hash(secrets, enc_algo, client_id_bytes),
            submission_id
        )

        result = unzip_and_decrypt_file(io.BytesIO(status_response.Result), secrets)

        mongo_client.ncdr_registry_status.update_one(
            {
                "case_id.$id": case_id,
                "submissions.submission_id": submission_id
            },
            {
                "$set": {
                    "submissions.$.result": str(result),
                    "submissions.$.status": status_response.Status
                }
            }
        )

        response.status_code = status.HTTP_200_OK
        return api_response(
            {"status": status_response.Status, "result": result},
            "Status fetched successfully",
            "success"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{case_id}/submissions", response_model=ResponseSchema)
async def get_all_submissions(
    request: Request,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    mongo_client: Database = request.app.database
    status_data = mongo_client.ncdr_registry_status.find_one({"case_id.$id": case_id})
    if not status_data:
        raise HTTPException(status_code=404, detail="No submission data found for this case")

    return api_response(
        {"submissions": status_data.get("submissions", [])},
        "All submissions retrieved successfully",
        "success"
    )
