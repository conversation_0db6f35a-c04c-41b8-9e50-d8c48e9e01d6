from pydantic import (
    BaseModel,
    field_validator,
    ValidationInfo,
    model_validator,
    PositiveFloat,
)
from datetime import date, time, datetime
from .patient_schema import PatientCaseSchema
from typing import Optional, Literal, List, Dict, Generic, TypeVar, Union
from .response_schema import DataSchema, IDNameSchema, OptionBaseSchema
from bson.objectid import ObjectId
from pymongo.database import DBRef


class CoordinatorPatientCaseSchema(PatientCaseSchema):
    address: str
    anticipated: Optional[str] = None


class NCDRSubmissionSchema(BaseModel):
    submission_id: Optional[str] = None
    status: Optional[str] = None
    result: Optional[str] = None
    timestamp: Optional[str] = None


class NCDRRegistryStatusSchema(BaseModel):
    last_submission: Optional[NCDRSubmissionSchema] = None
    submission_history: Optional[List[NCDRSubmissionSchema]] = []


class CoordinatorQueryParams(BaseModel):
    # type: Literal["pre-op", "procedure", "post-op", "completed"]
    type: Literal["pre-op", "procedure", "completed"]
    case_date: Optional[date] = None
    to_date: Optional[date] = None
    patient_dob: Optional[str] = None
    patient_name: Optional[str] = None
    patient_sex: Optional[str] = None
    completed_start_date: Optional[date] = None
    completed_end_date: Optional[date] = None

    @model_validator(mode="after")
    def date_converter(self):
        if self.type == "completed":
            assert (
                self.completed_start_date != None and self.completed_end_date != None
            ), f"for the type {self.type} completed start and end date is mandatory"
            self.completed_start_date = self.completed_start_date.strftime("%Y-%m-%d")
            self.completed_end_date = self.completed_end_date.strftime("%Y-%m-%d")

        # if self.type in ["procedure", "post-op"]:
        if self.type in ["procedure"]:
            assert (
                self.case_date != None and self.to_date != None
            ), f"for the type {self.type} case date and to date is mandatory"
        if self.case_date:
            self.case_date = self.case_date.strftime("%Y-%m-%d")
        if self.to_date:
            self.to_date = self.to_date.strftime("%Y-%m-%d")
        return self


class CoordinatorCaseListSchema(BaseModel):
    case_id: str
    patient: CoordinatorPatientCaseSchema
    procedure_date: Optional[date]
    procedure_time: Optional[time]
    anticoagulant: Optional[List[IDNameSchema]]
    verified: Optional[bool] = False
    ncdr_registry_status: Optional[NCDRRegistryStatusSchema] = None

    @field_validator("case_id", mode="before")
    def convert(cls, v, values: ValidationInfo):
        return str(v)


class DeviceIDNameSchema(IDNameSchema):
    device_size: Union[int, float, List[Union[int, float]]]


class ProcedureDetails(BaseModel):
    leak: Optional[bool] = None
    leak_value: Optional[int] = None
    device_size: Optional[Union[int, float]] = None
    device_type: Optional[str] = None
    anesthesia_type: Optional[str] = None
    complication_id: Optional[str] = None
    complication_other: Optional[str] = None
    complication_present: Optional[bool] = None

    class Config:
        extra = "forbid"

    @model_validator(mode="after")
    def modifier(self):
        if self.device_type:
            self.device_type = DBRef("device_type", ObjectId(self.device_type))

        if self.anesthesia_type:
            self.anesthesia_type = DBRef(
                "laao_string_selections", ObjectId(self.anesthesia_type)
            )

        if self.complication_id:
            self.complication_id = DBRef(
                "laao_string_selections", ObjectId(self.complication_id)
            )

        return self


class CoordLAAOProcedureUpdateSchema(BaseModel):
    implanting_physician_id: Optional[str] = None
    procedure_detail: Optional[ProcedureDetails] = None
    procedure_date: Optional[date] = None
    procedure_time: Optional[time] = None
    afib_ablation: Optional[bool] = None

    class Config:
        extra = "forbid"

    @model_validator(mode="after")
    def modifier(self):
        if self.implanting_physician_id:
            self.implanting_physician_id = DBRef(
                "implanting_physicians", ObjectId(self.implanting_physician_id)
            )

        if self.procedure_date:
            self.procedure_date = self.procedure_date.strftime("%Y-%m-%d")

        if self.procedure_time:
            self.procedure_time = self.procedure_time.strftime("%H:%M:%S")

        return self


T1 = TypeVar("T1")
T2 = TypeVar("T2")


class ComplicationOptionBaseSchema(BaseModel, Generic[T1, T2]):

    selected: T1
    options: List[T2]


class SelectedComplication(IDNameSchema):
    complication_other: Optional[str] = None
    complication_present: Optional[bool]


class CoordLAAOProcedureSchema(BaseModel):
    case_id: str
    procedure_time: Optional[time]
    procedure_date: Optional[date]
    case_detail_id: str
    implanting_physician: DataSchema
    device: OptionBaseSchema[DeviceIDNameSchema]
    anesthesia: OptionBaseSchema[IDNameSchema]
    leak: bool
    leak_value: Optional[int]
    complication: ComplicationOptionBaseSchema[SelectedComplication, IDNameSchema]
    afib_ablation: bool

    @field_validator("case_detail_id", "case_id", mode="before")
    def convert(cls, v, values: ValidationInfo):
        if v:
            return str(v)


class Address(BaseModel):
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None


class SocialHistorySchema(BaseModel):
    home_address: Optional[Address] = None
    distance: Optional[int] = None
    lives_independent: Optional[bool] = None
    has_social_support: Optional[bool] = None


class AnticoagulationItem(BaseModel):
    medicine: str
    quantity: str | int
    dosing_frequency: Optional[str] = None

    class Config:
        extra = "forbid"

    @model_validator(mode="after")
    def modifier(self):
        if self.medicine:
            self.medicine = DBRef("medicines", ObjectId(self.medicine))

        if self.dosing_frequency:
            self.dosing_frequency = DBRef(
                "laao_string_selections", ObjectId(self.dosing_frequency)
            )
        return self


class RelavantMedicalHistorySchema(BaseModel):
    major_comorbidities: Optional[str] = None
    prior_bleeding_events: Optional[str] = None
    dementia_presence: Optional[bool] = None


class CoordLAAOHistoryUpdate(BaseModel):
    implanting_physician_id: Optional[str] = None
    anticoagulant: Optional[List[AnticoagulationItem]] = None
    afib_classification_type_id: Optional[str] = None
    social_history: Optional[SocialHistorySchema] = None
    prior_ablation: Optional[str] = None
    prior_ablation_other: Optional[str] = None
    rationale: Optional[str] = None
    rationale_other: Optional[str] = None
    secondary_rationale: Optional[str] = None
    secondary_rationale_other: Optional[str] = None
    relavant_medical_history: Optional[RelavantMedicalHistorySchema] = None

    @model_validator(mode="after")
    def modify(self):
        if self.implanting_physician_id:
            self.implanting_physician_id = DBRef(
                "implanting_physicians", ObjectId(self.implanting_physician_id)
            )

        if self.afib_classification_type_id:
            self.afib_classification_type_id = DBRef(
                "afib_string_selections", ObjectId(self.afib_classification_type_id)
            )

        if self.prior_ablation:
            self.prior_ablation = DBRef(
                "laao_string_selections", ObjectId(self.prior_ablation)
            )

        if self.rationale:
            self.rationale = DBRef("laao_string_selections", ObjectId(self.rationale))

        if self.secondary_rationale:
            self.secondary_rationale = DBRef(
                "laao_string_selections", ObjectId(self.secondary_rationale)
            )

        return self


class ConsultVisitUpdateSchema(BaseModel):
    visit_date: date
    physician_id: str
    procedure_schedule_date: Optional[date] = None

    class Config:
        extra = "forbid"

    @model_validator(mode="after")
    def date_converter(self):
        if self.visit_date:
            self.visit_date = self.visit_date.strftime("%Y-%m-%d")

        if self.procedure_schedule_date:
            self.procedure_schedule_date = self.procedure_schedule_date.strftime(
                "%Y-%m-%d"
            )

        if self.physician_id:
            self.physician_id = DBRef(
                "implanting_physicians", ObjectId(self.physician_id)
            )

        return self


class PreOpLabUpdateSchema(BaseModel):
    hemoglobin: Optional[PositiveFloat] = None
    platelets: Optional[PositiveFloat] = None
    creatinine: Optional[PositiveFloat] = None
    egfr: Optional[PositiveFloat] = None
    rhythm_id: Optional[str] = None
    ventricular_rate: Optional[PositiveFloat] = None

    @model_validator(mode="after")
    def modify(self):
        if self.rhythm_id:
            self.rhythm_id = DBRef(
                "afib_string_selections", ObjectId(self.rhythm_id)
            )

        return self
